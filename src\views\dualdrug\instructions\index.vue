<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :rules="queryRules" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="医保药品编码" prop="medListCodg">
        <el-input
          v-model="queryParams.medListCodg"
          placeholder="请输入药品名称/药品编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['dualdrug:instructions:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['dualdrug:instructions:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['dualdrug:instructions:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['dualdrug:instructions:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
<!--      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
<!--    </el-row>-->

    <el-table v-loading="loading" :data="instructionsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="医保药品编码" align="center" prop="medListCodg" show-overflow-tooltip width="150"/>
      <el-table-column label="英文名/拼音" align="center" prop="englishName" show-overflow-tooltip width="150"/>
      <el-table-column label="性状" align="center" prop="TRAITS" show-overflow-tooltip width="120"/>
      <el-table-column label="成份/原料" align="center" prop="INGREDIENTS" show-overflow-tooltip width="150"/>
      <el-table-column label="功能主治/适应症" align="center" prop="INDICATIONS" show-overflow-tooltip width="180"/>
      <el-table-column label="用法用量" align="center" prop="DOSAGE" show-overflow-tooltip width="150"/>
      <el-table-column label="不良反应" align="center" prop="adverseReactions" show-overflow-tooltip width="150"/>
      <el-table-column label="禁忌" align="center" prop="TABOO" show-overflow-tooltip width="120"/>
      <el-table-column label="注意事项" align="center" prop="PRECAUTIONS" show-overflow-tooltip width="150"/>
      <el-table-column label="药物相互作用" align="center" prop="INTERACTION" show-overflow-tooltip width="150"/>
      <el-table-column label="孕妇及哺乳期妇女用药" align="center" prop="medicationForPregnantAndLactatingWomen" show-overflow-tooltip width="200"/>
      <el-table-column label="老年患者用药" align="center" prop="medicationForElderlyPatients" show-overflow-tooltip width="150"/>
      <el-table-column label="儿童用药" align="center" prop="medicationForChildren" show-overflow-tooltip width="120"/>
      <el-table-column label="药理毒理" align="center" prop="pharmacologyAndtoxicology" show-overflow-tooltip width="120"/>
      <el-table-column label="药物过量" align="center" prop="OVERDOSE" show-overflow-tooltip width="120"/>
      <el-table-column label="药代动力学" align="center" prop="PHARMACOKINETICS" show-overflow-tooltip width="150"/>
      <el-table-column label="贮藏" align="center" prop="STORE" show-overflow-tooltip width="100"/>
      <el-table-column label="包装" align="center" prop="PACK" show-overflow-tooltip width="100"/>
      <el-table-column label="有效期" align="center" prop="validPeriod" show-overflow-tooltip width="120"/>
      <el-table-column label="执行标准" align="center" prop="executiveStandard" show-overflow-tooltip width="150"/>
      <el-table-column label="修订日期" align="center" prop="revisionDate" show-overflow-tooltip width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.revisionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效标志" align="center" prop="valiFlag" show-overflow-tooltip width="100"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dualdrug:instructions:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dualdrug:instructions:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改药品说明书信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医保药品编码" prop="medListCodg">
              <el-input v-model="form.medListCodg" placeholder="请输入医保药品编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名/拼音" prop="englishName">
              <el-input v-model="form.englishName" placeholder="请输入英文名/拼音" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性状" prop="TRAITS">
              <el-input v-model="form.TRAITS" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成份/原料" prop="INGREDIENTS">
              <el-input v-model="form.INGREDIENTS" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="功能主治/适应症" prop="INDICATIONS">
              <el-input v-model="form.INDICATIONS" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用法用量" prop="DOSAGE">
              <el-input v-model="form.DOSAGE" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="不良反应" prop="adverseReactions">
              <el-input v-model="form.adverseReactions" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="禁忌" prop="TABOO">
              <el-input v-model="form.TABOO" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="注意事项" prop="PRECAUTIONS">
              <el-input v-model="form.PRECAUTIONS" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药物相互作用" prop="INTERACTION">
              <el-input v-model="form.INTERACTION" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="孕妇及哺乳期妇女用药" prop="medicationForPregnantAndLactatingWomen">
              <el-input v-model="form.medicationForPregnantAndLactatingWomen" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="老年患者用药" prop="medicationForElderlyPatients">
              <el-input v-model="form.medicationForElderlyPatients" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="儿童用药" prop="medicationForChildren">
              <el-input v-model="form.medicationForChildren" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药理毒理" prop="pharmacologyAndtoxicology">
              <el-input v-model="form.pharmacologyAndtoxicology" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药物过量" prop="OVERDOSE">
              <el-input v-model="form.OVERDOSE" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药代动力学" prop="PHARMACOKINETICS">
              <el-input v-model="form.PHARMACOKINETICS" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="贮藏" prop="STORE">
              <el-input v-model="form.STORE" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装" prop="PACK">
              <el-input v-model="form.PACK" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="有效期" prop="validPeriod">
              <el-input v-model="form.validPeriod" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行标准" prop="executiveStandard">
              <el-input v-model="form.executiveStandard" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="修订日期" prop="revisionDate">
              <el-date-picker clearable
                v-model="form.revisionDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择修订日期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效标志" prop="valiFlag">
              <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据创建时间" prop="crteTime">
              <el-date-picker clearable
                v-model="form.crteTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择数据创建时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建人ID" prop="crterId">
              <el-input v-model="form.crterId" placeholder="请输入创建人ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建人姓名" prop="crterName">
              <el-input v-model="form.crterName" placeholder="请输入创建人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据更新时间" prop="updtTime">
              <el-date-picker clearable
                v-model="form.updtTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择数据更新时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经办人" prop="opterId">
              <el-input v-model="form.opterId" placeholder="请输入经办人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经办人姓名" prop="opterName">
              <el-input v-model="form.opterName" placeholder="请输入经办人姓名" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInstructions, getInstructions, delInstructions, addInstructions, updateInstructions } from "@/api/dualdrug/instructions"

export default {
  name: "Instructions",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 药品说明书信息表格数据
      instructionsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        medListCodg: null
      },
      // 表单参数
      form: {},
      // 查询表单校验
      queryRules: {
        medListCodg: [
          { required: true, message: "医保药品编码不能为空", trigger: "blur" }
        ]
      },
      // 表单校验
      rules: {
        medListCodg: [
          { required: true, message: "医保药品编码不能为空", trigger: "blur" }
        ],
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
      }
    }
  },
  methods: {
    /** 查询药品说明书信息列表 */
    getList() {
      this.loading = true
      listInstructions(this.queryParams).then(response => {
        this.instructionsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        drugInstructionsId: null,
        medListCodg: null,
        englishName: null,
        TRAITS: null,
        INGREDIENTS: null,
        INDICATIONS: null,
        DOSAGE: null,
        adverseReactions: null,
        TABOO: null,
        PRECAUTIONS: null,
        INTERACTION: null,
        medicationForPregnantAndLactatingWomen: null,
        medicationForElderlyPatients: null,
        medicationForChildren: null,
        pharmacologyAndtoxicology: null,
        OVERDOSE: null,
        PHARMACOKINETICS: null,
        STORE: null,
        PACK: null,
        validPeriod: null,
        executiveStandard: null,
        revisionDate: null,
        valiFlag: null,
        crteTime: null,
        crterId: null,
        crterName: null,
        updtTime: null,
        opterId: null,
        opterName: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.$refs["queryForm"].validate(valid => {
        if (valid) {
          this.queryParams.pageNum = 1
          this.getList()
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.drugInstructionsId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加药品说明书信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const drugInstructionsId = row.drugInstructionsId || this.ids
      getInstructions(drugInstructionsId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改药品说明书信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.drugInstructionsId != null) {
            updateInstructions(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addInstructions(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const drugInstructionsIds = row.drugInstructionsId || this.ids
      this.$modal.confirm('是否确认删除药品说明书信息编号为"' + drugInstructionsIds + '"的数据项？').then(function() {
        return delInstructions(drugInstructionsIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dualdrug/instructions/export', {
        ...this.queryParams
      }, `instructions_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
