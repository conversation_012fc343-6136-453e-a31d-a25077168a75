import request from '@/utils/request'

// 查询西药中成药信息列表
export function listWmtcmpatinfo(query) {
  return request({
    url: '/dualdrug/wmtcmpatinfo/list',
    method: 'get',
    params: query
  })
}

// 查询西药中成药信息详细
export function getWmtcmpatinfo(medListCodg) {
  return request({
    url: '/dualdrug/wmtcmpatinfo/' + medListCodg,
    method: 'get'
  })
}

// 新增西药中成药信息
export function addWmtcmpatinfo(data) {
  return request({
    url: '/dualdrug/wmtcmpatinfo',
    method: 'post',
    data: data
  })
}

// 修改西药中成药信息
export function updateWmtcmpatinfo(data) {
  return request({
    url: '/dualdrug/wmtcmpatinfo',
    method: 'put',
    data: data
  })
}

// 删除西药中成药信息
export function delWmtcmpatinfo(medListCodg) {
  return request({
    url: '/dualdrug/wmtcmpatinfo/' + medListCodg,
    method: 'delete'
  })
}
