<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="通用名编码" prop="gennameCodg">
        <el-input
          v-model="queryParams.gennameCodg"
          placeholder="请输入通用名编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dualdrug:wmtcmpatinfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dualdrug:wmtcmpatinfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dualdrug:wmtcmpatinfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dualdrug:wmtcmpatinfo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="wmtcmpatinfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="医疗目录编码" align="center" prop="medListCodg" />
      <el-table-column label="药品商品名" align="center" prop="drugProdname" />
      <el-table-column label="通用名编码" align="center" prop="gennameCodg" />
      <el-table-column label="药品通用名" align="center" prop="drugGenname" />
      <el-table-column label="化学名称" align="center" prop="CHEMNAME" />
      <el-table-column label="别名" align="center" prop="ALIS" />
      <el-table-column label="英文名称" align="center" prop="engName" />
      <el-table-column label="注册名称" align="center" prop="regName" />
      <el-table-column label="药品本位码" align="center" prop="DRUGSTDCODE" />
      <el-table-column label="药品剂型" align="center" prop="drugDosform" />
      <el-table-column label="药品剂型名称" align="center" prop="drugDosformName" />
      <el-table-column label="药品类别" align="center" prop="drugType" />
      <el-table-column label="药品类别名称" align="center" prop="drugTypeName" />
      <el-table-column label="药品规格" align="center" prop="drugSpec" />
      <el-table-column label="药品规格代码" align="center" prop="drugSpecCode" />
      <el-table-column label="注册剂型" align="center" prop="regDosform" />
      <el-table-column label="注册规格" align="center" prop="regSpec" />
      <el-table-column label="注册规格代码" align="center" prop="regSpecCode" />
      <el-table-column label="每次用量" align="center" prop="eachDos" />
      <el-table-column label="使用频次" align="center" prop="usedFrqu" />
      <el-table-column label="酸根盐基" align="center" prop="ACDBAS" />
      <el-table-column label="国家药品编号" align="center" prop="natDrugNo" />
      <el-table-column label="使用方法" align="center" prop="usedMtd" />
      <el-table-column label="中成药标志" align="center" prop="tcmpatFlag" />
      <el-table-column label="生产地类别" align="center" prop="prodplacType" />
      <el-table-column label="生产地类别名称" align="center" prop="prodplacTypeName" />
      <el-table-column label="计价单位类型" align="center" prop="prcuntType" />
      <el-table-column label="非处方药标志" align="center" prop="otcFlag" />
      <el-table-column label="非处方药标志名称" align="center" prop="otcFlagName" />
      <el-table-column label="包装材质" align="center" prop="PACMATL" />
      <el-table-column label="包装材质名称" align="center" prop="pacmatlName" />
      <el-table-column label="包装规格" align="center" prop="PACSPEC" />
      <el-table-column label="包装数量" align="center" prop="pacCnt" />
      <el-table-column label="功能主治" align="center" prop="efccAtd" />
      <el-table-column label="给药途径" align="center" prop="RUTE" />
      <el-table-column label="说明书" align="center" prop="MANL" />
      <el-table-column label="开始日期" align="center" prop="BEGNDATE" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.BEGNDATE, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="ENDDATE" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ENDDATE, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最小使用单位" align="center" prop="minUseunt" />
      <el-table-column label="最小销售单位" align="center" prop="minSalunt" />
      <el-table-column label="最小计量单位" align="center" prop="minUnt" />
      <el-table-column label="最小包装数量" align="center" prop="minPacCnt" />
      <el-table-column label="最小包装单位" align="center" prop="minPacunt" />
      <el-table-column label="最小制剂单位" align="center" prop="minPrepunt" />
      <el-table-column label="${comment}" align="center" prop="minpacuntName" />
      <el-table-column label="最小制剂单位名称" align="center" prop="minPrepuntName" />
      <el-table-column label="转换比" align="center" prop="CONVRAT" />
      <el-table-column label="药品有效期" align="center" prop="drugExpy" />
      <el-table-column label="最小计价单位" align="center" prop="minPrcunt" />
      <el-table-column label="五笔助记码" align="center" prop="WUBI" />
      <el-table-column label="拼音助记码" align="center" prop="PINYIN" />
      <el-table-column label="分包装厂家" align="center" prop="subpckFcty" />
      <el-table-column label="生产企业代码" align="center" prop="prodentpCode" />
      <el-table-column label="生产企业名称" align="center" prop="prodentpName" />
      <el-table-column label="特殊限价药品标志" align="center" prop="spLmtpricDrugFlag" />
      <el-table-column label="特殊药品标志" align="center" prop="spDrugFlag" />
      <el-table-column label="限制使用范围" align="center" prop="lmtUsescp" />
      <el-table-column label="限制使用标志" align="center" prop="lmtUsedFlag" />
      <el-table-column label="药品注册证编号" align="center" prop="drugRegno" />
      <el-table-column label="药品注册证号开始日期" align="center" prop="drugRegcertBegndate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.drugRegcertBegndate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="药品注册证号结束日期" align="center" prop="drugRegcertEnddate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.drugRegcertEnddate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批准文号" align="center" prop="APRVNO" />
      <el-table-column label="批准文号开始日期" align="center" prop="aprvnoBegndate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.aprvnoBegndate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批准文号结束日期" align="center" prop="aprvnoEnddate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.aprvnoEnddate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="市场状态" align="center" prop="mktStas" />
      <el-table-column label="市场状态名称" align="center" prop="mktStasName" />
      <el-table-column label="药品注册批件电子档案" align="center" prop="drugRegAppvletrElecacs" />
      <el-table-column label="药品补充申请批件电子档案" align="center" prop="splmAppyAppvletrFile" />
      <el-table-column label="国家医保药品目录备注" align="center" prop="natHiDruglistMemo" />
      <el-table-column label="基本药物标志名称" align="center" prop="basMednFlagName" />
      <el-table-column label="基本药物标志" align="center" prop="basMednFlag" />
      <el-table-column label="增值税调整药品标志" align="center" prop="advaltaxAdjmDrugFlag" />
      <el-table-column label="增值税调整药品名称" align="center" prop="advaltaxAdjmDrugName" />
      <el-table-column label="上市药品目录集药品" align="center" prop="lstdDruglistDrug" />
      <el-table-column label="医保谈判药品标志" align="center" prop="hiNegoDrugFlag" />
      <el-table-column label="医保谈判药品名称" align="center" prop="hiNegoDrugName" />
      <el-table-column label="卫健委药品编码" align="center" prop="nhcDrugCodg" />
      <el-table-column label="备注" align="center" prop="MEMO" />
      <el-table-column label="有效标志" align="center" prop="valiFlag" />
      <el-table-column label="数据唯一记录号" align="center" prop="RID" />
      <el-table-column label="版本号" align="center" prop="VER" />
      <el-table-column label="版本名称" align="center" prop="verName" />
      <el-table-column label="儿童用药" align="center" prop="chldMedc" />
      <el-table-column label="公司名称" align="center" prop="coName" />
      <el-table-column label="仿制药一致性评价药品" align="center" prop="consevalDrug" />
      <el-table-column label="经销企业" align="center" prop="DSTR" />
      <el-table-column label="经销企业联系人" align="center" prop="dstrConer" />
      <el-table-column label="经销企业授权书电子档案" align="center" prop="dstrAuthFileElecacs" />
      <el-table-column label="国家医保药品目录剂型" align="center" prop="natHiDruglistDosform" />
      <el-table-column label="国家医保药品目录甲乙类标识" align="center" prop="natHiDruglistChrgitmLv" />
      <el-table-column label="上市许可证持有人" align="center" prop="lstdLicHolder" />
      <el-table-column label="下发标志" align="center" prop="isuFlag" />
      <el-table-column label="传输数据ID" align="center" prop="tramDataId" />
      <el-table-column label="生效时间" align="center" prop="efftTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.efftTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="失效时间" align="center" prop="invdTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.invdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="子版本名称" align="center" prop="smlVerName" />
      <el-table-column label="医保支付标准" align="center" prop="hiPayStd" />
      <el-table-column label="国家采集标识" align="center" prop="natClctFlag" />
      <el-table-column label="原药品编码" align="center" prop="initDrugCodg" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dualdrug:wmtcmpatinfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dualdrug:wmtcmpatinfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改西药中成药信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="药品商品名" prop="drugProdname">
          <el-input v-model="form.drugProdname" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="通用名编码" prop="gennameCodg">
          <el-input v-model="form.gennameCodg" placeholder="请输入通用名编码" />
        </el-form-item>
        <el-form-item label="药品通用名" prop="drugGenname">
          <el-input v-model="form.drugGenname" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="化学名称" prop="CHEMNAME">
          <el-input v-model="form.CHEMNAME" placeholder="请输入化学名称" />
        </el-form-item>
        <el-form-item label="别名" prop="ALIS">
          <el-input v-model="form.ALIS" placeholder="请输入别名" />
        </el-form-item>
        <el-form-item label="英文名称" prop="engName">
          <el-input v-model="form.engName" placeholder="请输入英文名称" />
        </el-form-item>
        <el-form-item label="注册名称" prop="regName">
          <el-input v-model="form.regName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="药品本位码" prop="DRUGSTDCODE">
          <el-input v-model="form.DRUGSTDCODE" placeholder="请输入药品本位码" />
        </el-form-item>
        <el-form-item label="药品剂型" prop="drugDosform">
          <el-input v-model="form.drugDosform" placeholder="请输入药品剂型" />
        </el-form-item>
        <el-form-item label="药品剂型名称" prop="drugDosformName">
          <el-input v-model="form.drugDosformName" placeholder="请输入药品剂型名称" />
        </el-form-item>
        <el-form-item label="药品类别名称" prop="drugTypeName">
          <el-input v-model="form.drugTypeName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="药品规格" prop="drugSpec">
          <el-input v-model="form.drugSpec" placeholder="请输入药品规格" />
        </el-form-item>
        <el-form-item label="药品规格代码" prop="drugSpecCode">
          <el-input v-model="form.drugSpecCode" placeholder="请输入药品规格代码" />
        </el-form-item>
        <el-form-item label="注册剂型" prop="regDosform">
          <el-input v-model="form.regDosform" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="注册规格" prop="regSpec">
          <el-input v-model="form.regSpec" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="注册规格代码" prop="regSpecCode">
          <el-input v-model="form.regSpecCode" placeholder="请输入注册规格代码" />
        </el-form-item>
        <el-form-item label="每次用量" prop="eachDos">
          <el-input v-model="form.eachDos" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="使用频次" prop="usedFrqu">
          <el-input v-model="form.usedFrqu" placeholder="请输入使用频次" />
        </el-form-item>
        <el-form-item label="酸根盐基" prop="ACDBAS">
          <el-input v-model="form.ACDBAS" placeholder="请输入酸根盐基" />
        </el-form-item>
        <el-form-item label="国家药品编号" prop="natDrugNo">
          <el-input v-model="form.natDrugNo" placeholder="请输入国家药品编号" />
        </el-form-item>
        <el-form-item label="使用方法" prop="usedMtd">
          <el-input v-model="form.usedMtd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="中成药标志" prop="tcmpatFlag">
          <el-input v-model="form.tcmpatFlag" placeholder="请输入中成药标志" />
        </el-form-item>
        <el-form-item label="生产地类别名称" prop="prodplacTypeName">
          <el-input v-model="form.prodplacTypeName" placeholder="请输入生产地类别名称" />
        </el-form-item>
        <el-form-item label="非处方药标志" prop="otcFlag">
          <el-input v-model="form.otcFlag" placeholder="请输入非处方药标志" />
        </el-form-item>
        <el-form-item label="非处方药标志名称" prop="otcFlagName">
          <el-input v-model="form.otcFlagName" placeholder="请输入非处方药标志名称" />
        </el-form-item>
        <el-form-item label="包装材质" prop="PACMATL">
          <el-input v-model="form.PACMATL" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="包装材质名称" prop="pacmatlName">
          <el-input v-model="form.pacmatlName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="包装规格" prop="PACSPEC">
          <el-input v-model="form.PACSPEC" placeholder="请输入包装规格" />
        </el-form-item>
        <el-form-item label="包装数量" prop="pacCnt">
          <el-input v-model="form.pacCnt" placeholder="请输入包装数量" />
        </el-form-item>
        <el-form-item label="功能主治" prop="efccAtd">
          <el-input v-model="form.efccAtd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="给药途径" prop="RUTE">
          <el-input v-model="form.RUTE" placeholder="请输入给药途径" />
        </el-form-item>
        <el-form-item label="说明书" prop="MANL">
          <el-input v-model="form.MANL" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="开始日期" prop="BEGNDATE">
          <el-date-picker clearable
            v-model="form.BEGNDATE"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="ENDDATE">
          <el-date-picker clearable
            v-model="form.ENDDATE"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最小使用单位" prop="minUseunt">
          <el-input v-model="form.minUseunt" placeholder="请输入最小使用单位" />
        </el-form-item>
        <el-form-item label="最小销售单位" prop="minSalunt">
          <el-input v-model="form.minSalunt" placeholder="请输入最小销售单位" />
        </el-form-item>
        <el-form-item label="最小计量单位" prop="minUnt">
          <el-input v-model="form.minUnt" placeholder="请输入最小计量单位" />
        </el-form-item>
        <el-form-item label="最小包装数量" prop="minPacCnt">
          <el-input v-model="form.minPacCnt" placeholder="请输入最小包装数量" />
        </el-form-item>
        <el-form-item label="最小包装单位" prop="minPacunt">
          <el-input v-model="form.minPacunt" placeholder="请输入最小包装单位" />
        </el-form-item>
        <el-form-item label="最小制剂单位" prop="minPrepunt">
          <el-input v-model="form.minPrepunt" placeholder="请输入最小制剂单位" />
        </el-form-item>
        <el-form-item label="${comment}" prop="minpacuntName">
          <el-input v-model="form.minpacuntName" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="最小制剂单位名称" prop="minPrepuntName">
          <el-input v-model="form.minPrepuntName" placeholder="请输入最小制剂单位名称" />
        </el-form-item>
        <el-form-item label="转换比" prop="CONVRAT">
          <el-input v-model="form.CONVRAT" placeholder="请输入转换比" />
        </el-form-item>
        <el-form-item label="药品有效期" prop="drugExpy">
          <el-input v-model="form.drugExpy" placeholder="请输入药品有效期" />
        </el-form-item>
        <el-form-item label="最小计价单位" prop="minPrcunt">
          <el-input v-model="form.minPrcunt" placeholder="请输入最小计价单位" />
        </el-form-item>
        <el-form-item label="五笔助记码" prop="WUBI">
          <el-input v-model="form.WUBI" placeholder="请输入五笔助记码" />
        </el-form-item>
        <el-form-item label="拼音助记码" prop="PINYIN">
          <el-input v-model="form.PINYIN" placeholder="请输入拼音助记码" />
        </el-form-item>
        <el-form-item label="分包装厂家" prop="subpckFcty">
          <el-input v-model="form.subpckFcty" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="生产企业代码" prop="prodentpCode">
          <el-input v-model="form.prodentpCode" placeholder="请输入生产企业代码" />
        </el-form-item>
        <el-form-item label="生产企业名称" prop="prodentpName">
          <el-input v-model="form.prodentpName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="特殊限价药品标志" prop="spLmtpricDrugFlag">
          <el-input v-model="form.spLmtpricDrugFlag" placeholder="请输入特殊限价药品标志" />
        </el-form-item>
        <el-form-item label="特殊药品标志" prop="spDrugFlag">
          <el-input v-model="form.spDrugFlag" placeholder="请输入特殊药品标志" />
        </el-form-item>
        <el-form-item label="限制使用范围" prop="lmtUsescp">
          <el-input v-model="form.lmtUsescp" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="限制使用标志" prop="lmtUsedFlag">
          <el-input v-model="form.lmtUsedFlag" placeholder="请输入限制使用标志" />
        </el-form-item>
        <el-form-item label="药品注册证编号" prop="drugRegno">
          <el-input v-model="form.drugRegno" placeholder="请输入药品注册证编号" />
        </el-form-item>
        <el-form-item label="药品注册证号开始日期" prop="drugRegcertBegndate">
          <el-date-picker clearable
            v-model="form.drugRegcertBegndate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择药品注册证号开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="药品注册证号结束日期" prop="drugRegcertEnddate">
          <el-date-picker clearable
            v-model="form.drugRegcertEnddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择药品注册证号结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="批准文号" prop="APRVNO">
          <el-input v-model="form.APRVNO" placeholder="请输入批准文号" />
        </el-form-item>
        <el-form-item label="批准文号开始日期" prop="aprvnoBegndate">
          <el-date-picker clearable
            v-model="form.aprvnoBegndate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择批准文号开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="批准文号结束日期" prop="aprvnoEnddate">
          <el-date-picker clearable
            v-model="form.aprvnoEnddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择批准文号结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="市场状态" prop="mktStas">
          <el-input v-model="form.mktStas" placeholder="请输入市场状态" />
        </el-form-item>
        <el-form-item label="市场状态名称" prop="mktStasName">
          <el-input v-model="form.mktStasName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="药品注册批件电子档案" prop="drugRegAppvletrElecacs">
          <el-input v-model="form.drugRegAppvletrElecacs" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="药品补充申请批件电子档案" prop="splmAppyAppvletrFile">
          <file-upload v-model="form.splmAppyAppvletrFile"/>
        </el-form-item>
        <el-form-item label="国家医保药品目录备注" prop="natHiDruglistMemo">
          <el-input v-model="form.natHiDruglistMemo" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="基本药物标志名称" prop="basMednFlagName">
          <el-input v-model="form.basMednFlagName" placeholder="请输入基本药物标志名称" />
        </el-form-item>
        <el-form-item label="基本药物标志" prop="basMednFlag">
          <el-input v-model="form.basMednFlag" placeholder="请输入基本药物标志" />
        </el-form-item>
        <el-form-item label="增值税调整药品标志" prop="advaltaxAdjmDrugFlag">
          <el-input v-model="form.advaltaxAdjmDrugFlag" placeholder="请输入增值税调整药品标志" />
        </el-form-item>
        <el-form-item label="增值税调整药品名称" prop="advaltaxAdjmDrugName">
          <el-input v-model="form.advaltaxAdjmDrugName" placeholder="请输入增值税调整药品名称" />
        </el-form-item>
        <el-form-item label="上市药品目录集药品" prop="lstdDruglistDrug">
          <el-input v-model="form.lstdDruglistDrug" placeholder="请输入上市药品目录集药品" />
        </el-form-item>
        <el-form-item label="医保谈判药品标志" prop="hiNegoDrugFlag">
          <el-input v-model="form.hiNegoDrugFlag" placeholder="请输入医保谈判药品标志" />
        </el-form-item>
        <el-form-item label="医保谈判药品名称" prop="hiNegoDrugName">
          <el-input v-model="form.hiNegoDrugName" placeholder="请输入医保谈判药品名称" />
        </el-form-item>
        <el-form-item label="卫健委药品编码" prop="nhcDrugCodg">
          <el-input v-model="form.nhcDrugCodg" placeholder="请输入卫健委药品编码" />
        </el-form-item>
        <el-form-item label="备注" prop="MEMO">
          <el-input v-model="form.MEMO" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
        </el-form-item>
        <el-form-item label="数据唯一记录号" prop="RID">
          <el-input v-model="form.RID" placeholder="请输入数据唯一记录号" />
        </el-form-item>
        <el-form-item label="数据创建时间" prop="crteTime">
          <el-date-picker clearable
            v-model="form.crteTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="数据更新时间" prop="updtTime">
          <el-date-picker clearable
            v-model="form.updtTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据更新时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建人ID" prop="crterId">
          <el-input v-model="form.crterId" placeholder="请输入创建人ID" />
        </el-form-item>
        <el-form-item label="创建人姓名" prop="crterName">
          <el-input v-model="form.crterName" placeholder="请输入创建人姓名" />
        </el-form-item>
        <el-form-item label="创建机构编号" prop="crteOptinsNo">
          <el-input v-model="form.crteOptinsNo" placeholder="请输入创建机构编号" />
        </el-form-item>
        <el-form-item label="经办人ID" prop="opterId">
          <el-input v-model="form.opterId" placeholder="请输入经办人ID" />
        </el-form-item>
        <el-form-item label="经办人姓名" prop="opterName">
          <el-input v-model="form.opterName" placeholder="请输入经办人姓名" />
        </el-form-item>
        <el-form-item label="经办时间" prop="optTime">
          <el-date-picker clearable
            v-model="form.optTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择经办时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经办机构编号" prop="optinsNo">
          <el-input v-model="form.optinsNo" placeholder="请输入经办机构编号" />
        </el-form-item>
        <el-form-item label="版本号" prop="VER">
          <el-input v-model="form.VER" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="版本名称" prop="verName">
          <el-input v-model="form.verName" placeholder="请输入版本名称" />
        </el-form-item>
        <el-form-item label="儿童用药" prop="chldMedc">
          <el-input v-model="form.chldMedc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="公司名称" prop="coName">
          <el-input v-model="form.coName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="仿制药一致性评价药品" prop="consevalDrug">
          <el-input v-model="form.consevalDrug" placeholder="请输入仿制药一致性评价药品" />
        </el-form-item>
        <el-form-item label="经销企业" prop="DSTR">
          <el-input v-model="form.DSTR" placeholder="请输入经销企业" />
        </el-form-item>
        <el-form-item label="经销企业联系人" prop="dstrConer">
          <el-input v-model="form.dstrConer" placeholder="请输入经销企业联系人" />
        </el-form-item>
        <el-form-item label="经销企业授权书电子档案" prop="dstrAuthFileElecacs">
          <el-input v-model="form.dstrAuthFileElecacs" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="国家医保药品目录剂型" prop="natHiDruglistDosform">
          <el-input v-model="form.natHiDruglistDosform" placeholder="请输入国家医保药品目录剂型" />
        </el-form-item>
        <el-form-item label="国家医保药品目录甲乙类标识" prop="natHiDruglistChrgitmLv">
          <el-input v-model="form.natHiDruglistChrgitmLv" placeholder="请输入国家医保药品目录甲乙类标识" />
        </el-form-item>
        <el-form-item label="上市许可证持有人" prop="lstdLicHolder">
          <el-input v-model="form.lstdLicHolder" placeholder="请输入上市许可证持有人" />
        </el-form-item>
        <el-form-item label="下发标志" prop="isuFlag">
          <el-input v-model="form.isuFlag" placeholder="请输入下发标志" />
        </el-form-item>
        <el-form-item label="传输数据ID" prop="tramDataId">
          <el-input v-model="form.tramDataId" placeholder="请输入传输数据ID" />
        </el-form-item>
        <el-form-item label="生效时间" prop="efftTime">
          <el-date-picker clearable
            v-model="form.efftTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择生效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="失效时间" prop="invdTime">
          <el-date-picker clearable
            v-model="form.invdTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择失效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="子版本名称" prop="smlVerName">
          <el-input v-model="form.smlVerName" placeholder="请输入子版本名称" />
        </el-form-item>
        <el-form-item label="医保支付标准" prop="hiPayStd">
          <el-input v-model="form.hiPayStd" placeholder="请输入医保支付标准" />
        </el-form-item>
        <el-form-item label="国家采集标识" prop="natClctFlag">
          <el-input v-model="form.natClctFlag" placeholder="请输入国家采集标识" />
        </el-form-item>
        <el-form-item label="原药品编码" prop="initDrugCodg">
          <el-input v-model="form.initDrugCodg" placeholder="请输入原药品编码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWmtcmpatinfo, getWmtcmpatinfo, delWmtcmpatinfo, addWmtcmpatinfo, updateWmtcmpatinfo } from "@/api/dualdrug/wmtcmpatinfo"

export default {
  name: "Wmtcmpatinfo",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 西药中成药信息表格数据
      wmtcmpatinfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        drugProdname: null,
        gennameCodg: null,
        drugGenname: null,
        CHEMNAME: null,
        ALIS: null,
        engName: null,
        regName: null,
        DRUGSTDCODE: null,
        drugDosform: null,
        drugDosformName: null,
        drugType: null,
        drugTypeName: null,
        drugSpec: null,
        drugSpecCode: null,
        regDosform: null,
        regSpec: null,
        regSpecCode: null,
        eachDos: null,
        usedFrqu: null,
        ACDBAS: null,
        natDrugNo: null,
        usedMtd: null,
        tcmpatFlag: null,
        prodplacType: null,
        prodplacTypeName: null,
        prcuntType: null,
        otcFlag: null,
        otcFlagName: null,
        PACMATL: null,
        pacmatlName: null,
        PACSPEC: null,
        pacCnt: null,
        efccAtd: null,
        RUTE: null,
        MANL: null,
        BEGNDATE: null,
        ENDDATE: null,
        minUseunt: null,
        minSalunt: null,
        minUnt: null,
        minPacCnt: null,
        minPacunt: null,
        minPrepunt: null,
        minpacuntName: null,
        minPrepuntName: null,
        CONVRAT: null,
        drugExpy: null,
        minPrcunt: null,
        WUBI: null,
        PINYIN: null,
        subpckFcty: null,
        prodentpCode: null,
        prodentpName: null,
        spLmtpricDrugFlag: null,
        spDrugFlag: null,
        lmtUsescp: null,
        lmtUsedFlag: null,
        drugRegno: null,
        drugRegcertBegndate: null,
        drugRegcertEnddate: null,
        APRVNO: null,
        aprvnoBegndate: null,
        aprvnoEnddate: null,
        mktStas: null,
        mktStasName: null,
        drugRegAppvletrElecacs: null,
        splmAppyAppvletrFile: null,
        natHiDruglistMemo: null,
        basMednFlagName: null,
        basMednFlag: null,
        advaltaxAdjmDrugFlag: null,
        advaltaxAdjmDrugName: null,
        lstdDruglistDrug: null,
        hiNegoDrugFlag: null,
        hiNegoDrugName: null,
        nhcDrugCodg: null,
        MEMO: null,
        valiFlag: null,
        RID: null,
        crteTime: null,
        updtTime: null,
        crterId: null,
        crterName: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        VER: null,
        verName: null,
        chldMedc: null,
        coName: null,
        consevalDrug: null,
        DSTR: null,
        dstrConer: null,
        dstrAuthFileElecacs: null,
        natHiDruglistDosform: null,
        natHiDruglistChrgitmLv: null,
        lstdLicHolder: null,
        isuFlag: null,
        tramDataId: null,
        efftTime: null,
        invdTime: null,
        smlVerName: null,
        hiPayStd: null,
        natClctFlag: null,
        initDrugCodg: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
        RID: [
          { required: true, message: "数据唯一记录号不能为空", trigger: "blur" }
        ],
        crteTime: [
          { required: true, message: "数据创建时间不能为空", trigger: "blur" }
        ],
        updtTime: [
          { required: true, message: "数据更新时间不能为空", trigger: "blur" }
        ],
        isuFlag: [
          { required: true, message: "下发标志不能为空", trigger: "blur" }
        ],
      }
    }
  },
  methods: {
    /** 查询西药中成药信息列表 */
    getList() {
      this.loading = true
      listWmtcmpatinfo(this.queryParams).then(response => {
        this.wmtcmpatinfoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        medListCodg: null,
        drugProdname: null,
        gennameCodg: null,
        drugGenname: null,
        CHEMNAME: null,
        ALIS: null,
        engName: null,
        regName: null,
        DRUGSTDCODE: null,
        drugDosform: null,
        drugDosformName: null,
        drugType: null,
        drugTypeName: null,
        drugSpec: null,
        drugSpecCode: null,
        regDosform: null,
        regSpec: null,
        regSpecCode: null,
        eachDos: null,
        usedFrqu: null,
        ACDBAS: null,
        natDrugNo: null,
        usedMtd: null,
        tcmpatFlag: null,
        prodplacType: null,
        prodplacTypeName: null,
        prcuntType: null,
        otcFlag: null,
        otcFlagName: null,
        PACMATL: null,
        pacmatlName: null,
        PACSPEC: null,
        pacCnt: null,
        efccAtd: null,
        RUTE: null,
        MANL: null,
        BEGNDATE: null,
        ENDDATE: null,
        minUseunt: null,
        minSalunt: null,
        minUnt: null,
        minPacCnt: null,
        minPacunt: null,
        minPrepunt: null,
        minpacuntName: null,
        minPrepuntName: null,
        CONVRAT: null,
        drugExpy: null,
        minPrcunt: null,
        WUBI: null,
        PINYIN: null,
        subpckFcty: null,
        prodentpCode: null,
        prodentpName: null,
        spLmtpricDrugFlag: null,
        spDrugFlag: null,
        lmtUsescp: null,
        lmtUsedFlag: null,
        drugRegno: null,
        drugRegcertBegndate: null,
        drugRegcertEnddate: null,
        APRVNO: null,
        aprvnoBegndate: null,
        aprvnoEnddate: null,
        mktStas: null,
        mktStasName: null,
        drugRegAppvletrElecacs: null,
        splmAppyAppvletrFile: null,
        natHiDruglistMemo: null,
        basMednFlagName: null,
        basMednFlag: null,
        advaltaxAdjmDrugFlag: null,
        advaltaxAdjmDrugName: null,
        lstdDruglistDrug: null,
        hiNegoDrugFlag: null,
        hiNegoDrugName: null,
        nhcDrugCodg: null,
        MEMO: null,
        valiFlag: null,
        RID: null,
        crteTime: null,
        updtTime: null,
        crterId: null,
        crterName: null,
        crteOptinsNo: null,
        opterId: null,
        opterName: null,
        optTime: null,
        optinsNo: null,
        VER: null,
        verName: null,
        chldMedc: null,
        coName: null,
        consevalDrug: null,
        DSTR: null,
        dstrConer: null,
        dstrAuthFileElecacs: null,
        natHiDruglistDosform: null,
        natHiDruglistChrgitmLv: null,
        lstdLicHolder: null,
        isuFlag: null,
        tramDataId: null,
        efftTime: null,
        invdTime: null,
        smlVerName: null,
        hiPayStd: null,
        natClctFlag: null,
        initDrugCodg: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.medListCodg)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加西药中成药信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const medListCodg = row.medListCodg || this.ids
      getWmtcmpatinfo(medListCodg).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改西药中成药信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.medListCodg != null) {
            updateWmtcmpatinfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addWmtcmpatinfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const medListCodgs = row.medListCodg || this.ids
      this.$modal.confirm('是否确认删除西药中成药信息编号为"' + medListCodgs + '"的数据项？').then(function() {
        return delWmtcmpatinfo(medListCodgs)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dualdrug/wmtcmpatinfo/export', {
        ...this.queryParams
      }, `wmtcmpatinfo_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
