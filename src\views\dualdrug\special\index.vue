<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="通用名编码" prop="gennameCodg">
        <el-input
          v-model="queryParams.gennameCodg"
          placeholder="请输入通用名编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dualdrug:special:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dualdrug:special:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dualdrug:special:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dualdrug:special:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="specialList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="医疗目录编码" align="center" prop="medListCodg" width="120" show-overflow-tooltip />
      <el-table-column label="药品商品名" align="center" prop="drugProdname" width="150" show-overflow-tooltip />
      <el-table-column label="通用名编码" align="center" prop="gennameCodg" width="120" show-overflow-tooltip />
      <el-table-column label="药品通用名" align="center" prop="drugGenname" width="150" show-overflow-tooltip />
      <el-table-column label="注册名称" align="center" prop="regName" width="150" show-overflow-tooltip />
      <el-table-column label="药品本位码" align="center" prop="DRUGSTDCODE" width="120" show-overflow-tooltip />
      <el-table-column label="药品剂型" align="center" prop="drugDosform" width="100" show-overflow-tooltip />
      <el-table-column label="药品剂型名称" align="center" prop="drugDosformName" width="120" show-overflow-tooltip />
      <el-table-column label="药品类别" align="center" prop="drugType" width="100" show-overflow-tooltip />
      <el-table-column label="药品类别名称" align="center" prop="drugTypeName" width="120" show-overflow-tooltip />
      <el-table-column label="药品规格" align="center" prop="drugSpec" width="120" show-overflow-tooltip />
      <el-table-column label="药品规格代码" align="center" prop="drugSpecCode" width="120" show-overflow-tooltip />
      <el-table-column label="注册剂型" align="center" prop="regDosform" width="100" show-overflow-tooltip />
      <el-table-column label="注册规格" align="center" prop="regSpec" width="120" show-overflow-tooltip />
      <el-table-column label="注册规格代码" align="center" prop="regSpecCode" width="120" show-overflow-tooltip />
      <el-table-column label="每次用量" align="center" prop="eachDos" width="100" show-overflow-tooltip />
      <el-table-column label="使用频次" align="center" prop="usedFrqu" width="100" show-overflow-tooltip />
      <el-table-column label="国家药品编号" align="center" prop="natDrugNo" width="120" show-overflow-tooltip />
      <el-table-column label="中成药标志" align="center" prop="tcmpatFlag" width="100" show-overflow-tooltip />
      <el-table-column label="非处方药标志" align="center" prop="otcFlag" width="110" show-overflow-tooltip />
      <el-table-column label="非处方药标志名称" align="center" prop="otcFlagName" width="130" show-overflow-tooltip />
      <el-table-column label="包装材质" align="center" prop="PACMATL" width="100" show-overflow-tooltip />
      <el-table-column label="包装材质名称" align="center" prop="pacmatlName" width="120" show-overflow-tooltip />
      <el-table-column label="包装规格" align="center" prop="PACSPEC" width="100" show-overflow-tooltip />
      <el-table-column label="包装数量" align="center" prop="pacCnt" width="100" show-overflow-tooltip />
      <el-table-column label="功能主治" align="center" prop="efccAtd" width="150" show-overflow-tooltip />
      <el-table-column label="给药途径" align="center" prop="RUTE" width="100" show-overflow-tooltip />
      <el-table-column label="说明书" align="center" prop="MANL" width="120" show-overflow-tooltip />
      <el-table-column label="最小使用单位" align="center" prop="minUseunt" width="110" show-overflow-tooltip />
      <el-table-column label="最小销售单位" align="center" prop="minSalunt" width="110" show-overflow-tooltip />
      <el-table-column label="最小计量单位" align="center" prop="minUnt" width="110" show-overflow-tooltip />
      <el-table-column label="最小包装数量" align="center" prop="minPacCnt" width="110" show-overflow-tooltip />
      <el-table-column label="最小包装单位" align="center" prop="minPacunt" width="110" show-overflow-tooltip />
      <el-table-column label="最小制剂单位" align="center" prop="minPrepunt" width="110" show-overflow-tooltip />
      <el-table-column label="最小制剂单位名称" align="center" prop="minPrepuntName" width="130" show-overflow-tooltip />
      <el-table-column label="最小计价单位" align="center" prop="minPrcunt" width="110" show-overflow-tooltip />
      <el-table-column label="拼音助记码" align="center" prop="PINYIN" width="110" show-overflow-tooltip />
      <el-table-column label="分包装厂家" align="center" prop="subpckFcty" width="120" show-overflow-tooltip />
      <el-table-column label="生产企业名称" align="center" prop="prodentpName" width="150" show-overflow-tooltip />
      <el-table-column label="特殊限价药品标志" align="center" prop="spLmtpricDrugFlag" width="140" show-overflow-tooltip />
      <el-table-column label="普通药品标志" align="center" prop="orDrugFlag" width="110" show-overflow-tooltip />
      <el-table-column label="特殊药品标志" align="center" prop="spDrugFlag" width="110" show-overflow-tooltip />
      <el-table-column label="慢病药品标志" align="center" prop="chDrugFlag" width="110" show-overflow-tooltip />
      <el-table-column label="限制使用范围" align="center" prop="lmtUsescp" width="120" show-overflow-tooltip />
      <el-table-column label="限制使用标志" align="center" prop="lmtUsedFlag" width="110" show-overflow-tooltip />
      <el-table-column label="药品注册证编号" align="center" prop="drugRegno" width="140" show-overflow-tooltip />
      <el-table-column label="药品注册证号开始日期" align="center" prop="drugRegcertBegndate" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.drugRegcertBegndate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="药品注册证号结束日期" align="center" prop="drugRegcertEnddate" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.drugRegcertEnddate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批准文号" align="center" prop="APRVNO" width="120" show-overflow-tooltip />
      <el-table-column label="版本号" align="center" prop="VER" width="80" show-overflow-tooltip />
      <el-table-column label="国家采集标识" align="center" prop="natClctFlag" width="110" show-overflow-tooltip />
      <el-table-column label="统筹区编号" align="center" prop="poolareaNo" width="110" show-overflow-tooltip />
      <el-table-column label="统筹区名称" align="center" prop="poolareaName" width="120" show-overflow-tooltip />
      <el-table-column label="有效标志" align="center" prop="valiFlag" width="80" show-overflow-tooltip />
      <el-table-column label="数据创建时间" align="center" prop="crteTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.crteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人ID" align="center" prop="crterId" width="100" show-overflow-tooltip />
      <el-table-column label="创建人姓名" align="center" prop="crterName" width="110" show-overflow-tooltip />
      <el-table-column label="数据更新时间" align="center" prop="updtTime" width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updtTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经办人" align="center" prop="opterId" width="100" show-overflow-tooltip />
      <el-table-column label="经办人姓名" align="center" prop="opterName" width="110" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dualdrug:special:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dualdrug:special:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改双通道药品目录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="药品商品名" prop="drugProdname">
          <el-input v-model="form.drugProdname" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="通用名编码" prop="gennameCodg">
          <el-input v-model="form.gennameCodg" placeholder="请输入通用名编码" />
        </el-form-item>
        <el-form-item label="药品通用名" prop="drugGenname">
          <el-input v-model="form.drugGenname" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="注册名称" prop="regName">
          <el-input v-model="form.regName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="药品本位码" prop="DRUGSTDCODE">
          <el-input v-model="form.DRUGSTDCODE" placeholder="请输入药品本位码" />
        </el-form-item>
        <el-form-item label="药品剂型" prop="drugDosform">
          <el-input v-model="form.drugDosform" placeholder="请输入药品剂型" />
        </el-form-item>
        <el-form-item label="药品剂型名称" prop="drugDosformName">
          <el-input v-model="form.drugDosformName" placeholder="请输入药品剂型名称" />
        </el-form-item>
        <el-form-item label="药品类别名称" prop="drugTypeName">
          <el-input v-model="form.drugTypeName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="药品规格" prop="drugSpec">
          <el-input v-model="form.drugSpec" placeholder="请输入药品规格" />
        </el-form-item>
        <el-form-item label="药品规格代码" prop="drugSpecCode">
          <el-input v-model="form.drugSpecCode" placeholder="请输入药品规格代码" />
        </el-form-item>
        <el-form-item label="注册剂型" prop="regDosform">
          <el-input v-model="form.regDosform" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="注册规格" prop="regSpec">
          <el-input v-model="form.regSpec" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="注册规格代码" prop="regSpecCode">
          <el-input v-model="form.regSpecCode" placeholder="请输入注册规格代码" />
        </el-form-item>
        <el-form-item label="每次用量" prop="eachDos">
          <el-input v-model="form.eachDos" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="使用频次" prop="usedFrqu">
          <el-input v-model="form.usedFrqu" placeholder="请输入使用频次" />
        </el-form-item>
        <el-form-item label="国家药品编号" prop="natDrugNo">
          <el-input v-model="form.natDrugNo" placeholder="请输入国家药品编号" />
        </el-form-item>
        <el-form-item label="中成药标志" prop="tcmpatFlag">
          <el-input v-model="form.tcmpatFlag" placeholder="请输入中成药标志" />
        </el-form-item>
        <el-form-item label="非处方药标志" prop="otcFlag">
          <el-input v-model="form.otcFlag" placeholder="请输入非处方药标志" />
        </el-form-item>
        <el-form-item label="非处方药标志名称" prop="otcFlagName">
          <el-input v-model="form.otcFlagName" placeholder="请输入非处方药标志名称" />
        </el-form-item>
        <el-form-item label="包装材质" prop="PACMATL">
          <el-input v-model="form.PACMATL" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="包装材质名称" prop="pacmatlName">
          <el-input v-model="form.pacmatlName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="包装规格" prop="PACSPEC">
          <el-input v-model="form.PACSPEC" placeholder="请输入包装规格" />
        </el-form-item>
        <el-form-item label="包装数量" prop="pacCnt">
          <el-input v-model="form.pacCnt" placeholder="请输入包装数量" />
        </el-form-item>
        <el-form-item label="功能主治" prop="efccAtd">
          <el-input v-model="form.efccAtd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="给药途径" prop="RUTE">
          <el-input v-model="form.RUTE" placeholder="请输入给药途径" />
        </el-form-item>
        <el-form-item label="说明书" prop="MANL">
          <el-input v-model="form.MANL" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="最小使用单位" prop="minUseunt">
          <el-input v-model="form.minUseunt" placeholder="请输入最小使用单位" />
        </el-form-item>
        <el-form-item label="最小销售单位" prop="minSalunt">
          <el-input v-model="form.minSalunt" placeholder="请输入最小销售单位" />
        </el-form-item>
        <el-form-item label="最小计量单位" prop="minUnt">
          <el-input v-model="form.minUnt" placeholder="请输入最小计量单位" />
        </el-form-item>
        <el-form-item label="最小包装数量" prop="minPacCnt">
          <el-input v-model="form.minPacCnt" placeholder="请输入最小包装数量" />
        </el-form-item>
        <el-form-item label="最小包装单位" prop="minPacunt">
          <el-input v-model="form.minPacunt" placeholder="请输入最小包装单位" />
        </el-form-item>
        <el-form-item label="最小制剂单位" prop="minPrepunt">
          <el-input v-model="form.minPrepunt" placeholder="请输入最小制剂单位" />
        </el-form-item>
        <el-form-item label="最小制剂单位名称" prop="minPrepuntName">
          <el-input v-model="form.minPrepuntName" placeholder="请输入最小制剂单位名称" />
        </el-form-item>
        <el-form-item label="最小计价单位" prop="minPrcunt">
          <el-input v-model="form.minPrcunt" placeholder="请输入最小计价单位" />
        </el-form-item>
        <el-form-item label="拼音助记码" prop="PINYIN">
          <el-input v-model="form.PINYIN" placeholder="请输入拼音助记码" />
        </el-form-item>
        <el-form-item label="分包装厂家" prop="subpckFcty">
          <el-input v-model="form.subpckFcty" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="生产企业名称" prop="prodentpName">
          <el-input v-model="form.prodentpName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="特殊限价药品标志" prop="spLmtpricDrugFlag">
          <el-input v-model="form.spLmtpricDrugFlag" placeholder="请输入特殊限价药品标志" />
        </el-form-item>
        <el-form-item label="普通药品标志" prop="orDrugFlag">
          <el-input v-model="form.orDrugFlag" placeholder="请输入普通药品标志" />
        </el-form-item>
        <el-form-item label="特殊药品标志" prop="spDrugFlag">
          <el-input v-model="form.spDrugFlag" placeholder="请输入特殊药品标志" />
        </el-form-item>
        <el-form-item label="慢病药品标志" prop="chDrugFlag">
          <el-input v-model="form.chDrugFlag" placeholder="请输入慢病药品标志" />
        </el-form-item>
        <el-form-item label="限制使用范围" prop="lmtUsescp">
          <el-input v-model="form.lmtUsescp" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="限制使用标志" prop="lmtUsedFlag">
          <el-input v-model="form.lmtUsedFlag" placeholder="请输入限制使用标志" />
        </el-form-item>
        <el-form-item label="药品注册证编号" prop="drugRegno">
          <el-input v-model="form.drugRegno" placeholder="请输入药品注册证编号" />
        </el-form-item>
        <el-form-item label="药品注册证号开始日期" prop="drugRegcertBegndate">
          <el-date-picker clearable
            v-model="form.drugRegcertBegndate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择药品注册证号开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="药品注册证号结束日期" prop="drugRegcertEnddate">
          <el-date-picker clearable
            v-model="form.drugRegcertEnddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择药品注册证号结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="批准文号" prop="APRVNO">
          <el-input v-model="form.APRVNO" placeholder="请输入批准文号" />
        </el-form-item>
        <el-form-item label="版本号" prop="VER">
          <el-input v-model="form.VER" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="国家采集标识" prop="natClctFlag">
          <el-input v-model="form.natClctFlag" placeholder="请输入国家采集标识" />
        </el-form-item>
        <el-form-item label="统筹区编号" prop="poolareaNo">
          <el-input v-model="form.poolareaNo" placeholder="请输入统筹区编号" />
        </el-form-item>
        <el-form-item label="统筹区名称" prop="poolareaName">
          <el-input v-model="form.poolareaName" placeholder="请输入统筹区名称" />
        </el-form-item>
        <el-form-item label="有效标志" prop="valiFlag">
          <el-input v-model="form.valiFlag" placeholder="请输入有效标志" />
        </el-form-item>
        <el-form-item label="数据创建时间" prop="crteTime">
          <el-date-picker clearable
            v-model="form.crteTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建人ID" prop="crterId">
          <el-input v-model="form.crterId" placeholder="请输入创建人ID" />
        </el-form-item>
        <el-form-item label="创建人姓名" prop="crterName">
          <el-input v-model="form.crterName" placeholder="请输入创建人姓名" />
        </el-form-item>
        <el-form-item label="数据更新时间" prop="updtTime">
          <el-date-picker clearable
            v-model="form.updtTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择数据更新时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="经办人" prop="opterId">
          <el-input v-model="form.opterId" placeholder="请输入经办人" />
        </el-form-item>
        <el-form-item label="经办人姓名" prop="opterName">
          <el-input v-model="form.opterName" placeholder="请输入经办人姓名" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSpecial, getSpecial, delSpecial, addSpecial, updateSpecial } from "@/api/dualdrug/special"

export default {
  name: "Special",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 双通道药品目录表格数据
      specialList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        drugProdname: null,
        gennameCodg: null,
        drugGenname: null,
        regName: null,
        DRUGSTDCODE: null,
        drugDosform: null,
        drugDosformName: null,
        drugType: null,
        drugTypeName: null,
        drugSpec: null,
        drugSpecCode: null,
        regDosform: null,
        regSpec: null,
        regSpecCode: null,
        eachDos: null,
        usedFrqu: null,
        natDrugNo: null,
        tcmpatFlag: null,
        otcFlag: null,
        otcFlagName: null,
        PACMATL: null,
        pacmatlName: null,
        PACSPEC: null,
        pacCnt: null,
        efccAtd: null,
        RUTE: null,
        MANL: null,
        minUseunt: null,
        minSalunt: null,
        minUnt: null,
        minPacCnt: null,
        minPacunt: null,
        minPrepunt: null,
        minPrepuntName: null,
        minPrcunt: null,
        PINYIN: null,
        subpckFcty: null,
        prodentpName: null,
        spLmtpricDrugFlag: null,
        orDrugFlag: null,
        spDrugFlag: null,
        chDrugFlag: null,
        lmtUsescp: null,
        lmtUsedFlag: null,
        drugRegno: null,
        drugRegcertBegndate: null,
        drugRegcertEnddate: null,
        APRVNO: null,
        VER: null,
        natClctFlag: null,
        poolareaNo: null,
        poolareaName: null,
        valiFlag: null,
        crteTime: null,
        crterId: null,
        crterName: null,
        updtTime: null,
        opterId: null,
        opterName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        valiFlag: [
          { required: true, message: "有效标志不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询双通道药品目录列表 */
    getList() {
      this.loading = true
      listSpecial(this.queryParams).then(response => {
        this.specialList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        medListCodg: null,
        drugProdname: null,
        gennameCodg: null,
        drugGenname: null,
        regName: null,
        DRUGSTDCODE: null,
        drugDosform: null,
        drugDosformName: null,
        drugType: null,
        drugTypeName: null,
        drugSpec: null,
        drugSpecCode: null,
        regDosform: null,
        regSpec: null,
        regSpecCode: null,
        eachDos: null,
        usedFrqu: null,
        natDrugNo: null,
        tcmpatFlag: null,
        otcFlag: null,
        otcFlagName: null,
        PACMATL: null,
        pacmatlName: null,
        PACSPEC: null,
        pacCnt: null,
        efccAtd: null,
        RUTE: null,
        MANL: null,
        minUseunt: null,
        minSalunt: null,
        minUnt: null,
        minPacCnt: null,
        minPacunt: null,
        minPrepunt: null,
        minPrepuntName: null,
        minPrcunt: null,
        PINYIN: null,
        subpckFcty: null,
        prodentpName: null,
        spLmtpricDrugFlag: null,
        orDrugFlag: null,
        spDrugFlag: null,
        chDrugFlag: null,
        lmtUsescp: null,
        lmtUsedFlag: null,
        drugRegno: null,
        drugRegcertBegndate: null,
        drugRegcertEnddate: null,
        APRVNO: null,
        VER: null,
        natClctFlag: null,
        poolareaNo: null,
        poolareaName: null,
        valiFlag: null,
        crteTime: null,
        crterId: null,
        crterName: null,
        updtTime: null,
        opterId: null,
        opterName: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.medListCodg)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加双通道药品目录"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const medListCodg = row.medListCodg || this.ids
      getSpecial(medListCodg).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改双通道药品目录"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.medListCodg != null) {
            updateSpecial(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addSpecial(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const medListCodgs = row.medListCodg || this.ids
      this.$modal.confirm('是否确认删除双通道药品目录编号为"' + medListCodgs + '"的数据项？').then(function() {
        return delSpecial(medListCodgs)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dualdrug/special/export', {
        ...this.queryParams
      }, `special_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
