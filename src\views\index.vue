<template>
  <div class="app-container">
    <!-- 系统标题 -->
    <div class="system-header">
      <h1 class="system-title">双通道药品管理系统</h1>
      <p class="system-subtitle">Dual Channel Drug Management System</p>
    </div>
    
    <!-- 功能介绍 -->
    <el-row :gutter="20" class="feature-section">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="card-header">
            <span>系统功能</span>
          </div>
          <div class="feature-grid">
            <div class="feature-item">
              <div class="feature-icon">
                <i class="el-icon-medicine"></i>
              </div>
              <h3>药品信息管理</h3>
              <p>管理双通道药品的基本信息，包括药品编码、名称、规格、生产企业等详细信息</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <i class="el-icon-star-on"></i>
              </div>
              <h3>特殊药品监管</h3>
              <p>针对特殊药品进行专门管理，确保药品使用的安全性和合规性</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <i class="el-icon-collection"></i>
              </div>
              <h3>中成药管理</h3>
              <p>专门管理中成药信息，包括功能主治、用法用量等中医药特色信息</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <i class="el-icon-data-analysis"></i>
              </div>
              <h3>数据分析</h3>
              <p>提供药品数据的统计分析功能，支持多维度的数据查询和报表生成</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.9.0",
      // 统计数据
      drugCount: 0,
      specialDrugCount: 0,
      tcmCount: 0,
      todayUpdateCount: 0,
      lastUpdateTime: ""
    }
  },
  created() {
    this.loadStatistics()
  },
  methods: {
    // 加载统计数据
    loadStatistics() {
      // 模拟数据，实际应该调用API获取
      this.drugCount = 15680
      this.specialDrugCount = 1256
      this.tcmCount = 3420
      this.todayUpdateCount = 45
      this.lastUpdateTime = this.formatDate(new Date())
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 跳转到特殊药品管理
    goToSpecialDrug() {
      this.$router.push('/dualdrug/special')
    },

    // 跳转到中成药管理
    goToTcmDrug() {
      this.$router.push('/dualdrug/wmtcmpatinfo')
    },

    // 数据导入
    goToDataImport() {
      this.$message.info('数据导入功能开发中...')
    },

    // 数据导出
    goToDataExport() {
      this.$message.info('数据导出功能开发中...')
    },

    goTarget(href) {
      window.open(href, "_blank")
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

// 系统标题
.system-header {
  text-align: center;
  margin-bottom: 30px;

  .system-title {
    font-size: 32px;
    color: #303133;
    margin: 0 0 10px 0;
    font-weight: 600;
  }

  .system-subtitle {
    font-size: 16px;
    color: #909399;
    margin: 0;
  }
}

// 统计卡片
.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;

  i {
    font-size: 24px;
    color: white;
  }

  &.drug-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.special-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  &.tcm-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  &.update-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

// 快捷操作和系统信息
.quick-actions {
  margin-bottom: 30px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.quick-action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
    transform: translateY(-2px);
  }

  i {
    font-size: 32px;
    color: #409eff;
    margin-bottom: 10px;
  }

  span {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }
}

.system-info {
  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .info-label {
    color: #909399;
    font-size: 14px;
  }

  .info-value {
    color: #303133;
    font-size: 14px;
    font-weight: 500;

    &.status-normal {
      color: #67c23a;
    }
  }
}

// 功能介绍
.feature-section {
  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    padding: 20px 0;
  }

  .feature-item {
    text-align: center;

    .feature-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;

      i {
        font-size: 32px;
        color: white;
      }
    }

    h3 {
      font-size: 18px;
      color: #303133;
      margin: 0 0 15px 0;
      font-weight: 600;
    }

    p {
      font-size: 14px;
      color: #606266;
      line-height: 1.6;
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-row {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .quick-action-grid {
    grid-template-columns: 1fr;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}
</style>

