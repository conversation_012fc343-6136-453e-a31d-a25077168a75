import request from '@/utils/request'

// 查询双通道药品目录列表
export function listSpecial(query) {
  return request({
    url: '/dualdrug/special/list',
    method: 'get',
    params: query
  })
}

// 查询双通道药品目录详细
export function getSpecial(medListCodg) {
  return request({
    url: '/dualdrug/special/' + medListCodg,
    method: 'get'
  })
}

// 新增双通道药品目录
export function addSpecial(data) {
  return request({
    url: '/dualdrug/special',
    method: 'post',
    data: data
  })
}

// 修改双通道药品目录
export function updateSpecial(data) {
  return request({
    url: '/dualdrug/special',
    method: 'put',
    data: data
  })
}

// 删除双通道药品目录
export function delSpecial(medListCodg) {
  return request({
    url: '/dualdrug/special/' + medListCodg,
    method: 'delete'
  })
}
