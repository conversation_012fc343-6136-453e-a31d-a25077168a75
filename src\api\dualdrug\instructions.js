import request from '@/utils/request'

// 查询药品说明书信息列表
export function listInstructions(query) {
  return request({
    url: '/dualdrug/instructions/list',
    method: 'get',
    params: query
  })
}

// 查询药品说明书信息详细
export function getInstructions(drugInstructionsId) {
  return request({
    url: '/dualdrug/instructions/' + drugInstructionsId,
    method: 'get'
  })
}

// 新增药品说明书信息
export function addInstructions(data) {
  return request({
    url: '/dualdrug/instructions',
    method: 'post',
    data: data
  })
}

// 修改药品说明书信息
export function updateInstructions(data) {
  return request({
    url: '/dualdrug/instructions',
    method: 'put',
    data: data
  })
}

// 删除药品说明书信息
export function delInstructions(drugInstructionsId) {
  return request({
    url: '/dualdrug/instructions/' + drugInstructionsId,
    method: 'delete'
  })
}
